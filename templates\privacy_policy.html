{% extends "base.html" %}
{% block auth_content %}

<div class="privacy-container">
    <div class="privacy-card">
        <div class="privacy-header">
            <div class="privacy-logo">
                <i class="fas fa-shield-alt"></i>
            </div>
            <h2>Terms & Conditions</h2>
            <p>Please read and accept our terms and conditions to continue using the GRS Wall Designer application. You will see this agreement on every login to ensure you're always aware of our current terms and policies.</p>
        </div>
        
        <div class="privacy-content">
            <div class="privacy-scroll">
                <h3>TERMS AND CONDITIONS / DISCLAIMER</h3>
                <h4>D I S C L A I M E R</h4>
                <p>The GRS Wall Designer software developed by Geosynapse Pvt. Ltd. is intended to support engineers in the design and analysis of geosynthetic reinforced soil (GRS) walls. The responsibility to ensure the appropriateness of input data, design methodology, and the accuracy of results lies solely with the user (i.e., qualified and experienced engineers). Users are expected to thoroughly review, interpret, and validate all results generated by the software.</p>
                
                <p>Geosynapse Pvt. Ltd. shall not be liable for any direct, indirect, incidental, or consequential damages arising from the use or misuse of the software, including loss of data or inaccurate design. It is the user's responsibility to ensure that all relevant parameters are considered.</p>
                
                <p>Geosynapse Pvt. Ltd. does not warrant that the GRS Wall Designer software is free from errors, bugs, or omissions. The software is provided "as is" without any warranty that it will function without interruption or be error-free. Users acknowledge that no software can be guaranteed to be entirely free from defects or vulnerabilities, and errors may exist in both code and output.</p>
                
                <p>Geosynapse Pvt. Ltd. and its team disclaim any responsibility or liability for any inaccuracies or failures, whether due to programming errors or limitations of the software's models or assumptions.</p>

                <h3>LICENSE AND USAGE</h3>
                <p>This software is licensed, not sold. By using this software, the user agrees to the following:</p>
                <ul>
                    <li>Access is granted via a subscription-based license. Redistribution, reverse-engineering, or unauthorized copying is strictly prohibited.</li>
                    <li>License is tied to the registered user account and/or validated hardware ID or USB key if applicable.</li>
                    <li>Users are not permitted to sublicense, resell, or commercially distribute the software without written consent from Geosynapse Pvt. Ltd.</li>
                </ul>

                <h3>OPEN-SOURCE COMPONENTS NOTICE</h3>
                <p>This software uses certain open-source components, including but not limited to:</p>
                <ul>
                    <li>Python and Flask (under the BSD License),</li>
                    <li>HTML/CSS/JavaScript libraries such as Bootstrap (MIT License),</li>
                    <li>Any additional third-party libraries are used in accordance with their respective licenses (e.g., MIT, Apache 2.0, BSD).</li>
                </ul>
                <p>We acknowledge and comply with the open-source licenses of these components. All such components are credited appropriately in the documentation or in the LICENSES folder within the installation directory or repository.</p>

                <h3>LIMITATION OF WARRANTY</h3>
                <p>ALL DATA AND OUTPUTS PROVIDED BY THIS SOFTWARE ARE PROVIDED "AS IS", WITHOUT WARRANTY OR CONDITION OF ANY KIND, EITHER EXPRESS OR IMPLIED. THIS INCLUDES, BUT IS NOT LIMITED TO, ANY IMPLIED WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, NON-INFRINGEMENT, OR THAT THE SOFTWARE WILL MEET YOUR REQUIREMENTS OR BE ERROR-FREE.</p>

                <h3>SUPPORT AND UPDATES</h3>
                <p>Support and software updates will be provided as per the subscription plan chosen by the user. Geosynapse Pvt. Ltd. reserves the right to modify or discontinue any part of the service with or without notice.</p>

                <h3>END-USER LICENSE AGREEMENT (EULA)</h3>
                <p>This End-User License Agreement ("Agreement") is a legal agreement between you (the "User") and Geosynapse Pvt. Ltd. ("Provider") for the use of the GRS Wall Designer web application (the "Software").</p>
                <p><strong>BY ACCESSING, SUBSCRIBING, OR USING THE SOFTWARE, YOU AGREE TO BE BOUND BY THIS AGREEMENT.</strong></p>

                <h4>1. GRANT OF LICENSE</h4>
                <p>You are granted a non-exclusive, non-transferable, limited license to access and use the Software for the duration of your active subscription.</p>

                <h4>2. SUBSCRIPTION AND ACCESS</h4>
                <p>The Software is provided on a subscription basis. Your access will continue only while your subscription remains active and in good standing.</p>

                <h4>3. INTELLECTUAL PROPERTY</h4>
                <p>All intellectual property rights in the Software, including but not limited to code, design, databases, and algorithms, belong solely to Geosynapse Pvt. Ltd. You may not copy, modify, reverse-engineer, decompile, or create derivative works.</p>

                <h4>4. RESTRICTIONS</h4>
                <p>You shall not:</p>
                <ul>
                    <li>Share or resell access to the Software</li>
                    <li>Attempt to bypass security mechanisms (including license validation or access control)</li>
                    <li>Host or mirror the Software</li>
                </ul>

                <h4>5. DATA AND PRIVACY</h4>
                <p>We may collect and store user information as detailed in our Privacy Policy. You are responsible for maintaining the confidentiality of your login credentials.</p>

                <h4>6. WARRANTY DISCLAIMER</h4>
                <p>The Software is provided "as is" without warranties of any kind. We do not guarantee uninterrupted or error-free service.</p>

                <h4>7. LIMITATION OF LIABILITY</h4>
                <p>Geosynapse Pvt. Ltd. shall not be liable for any indirect, incidental, or consequential damages, including loss of business or profits.</p>

                <h4>8. TERMINATION</h4>
                <p>Your license will terminate automatically if you fail to comply with the terms of this Agreement. Geosynapse Pvt. Ltd. may suspend or terminate access for non-compliance or security risks.</p>

                <h4>9. GOVERNING LAW</h4>
                <p>This Agreement shall be governed by the laws of India. Disputes shall be subject to the exclusive jurisdiction of the courts of Andhra Pradesh.</p>

                <h4>10. CONTACT</h4>
                <p>For questions, contact: <EMAIL></p>

                <h3>PRIVACY POLICY</h3>
                <p>Geosynapse Pvt. Ltd. ("we", "us", or "our") respects your privacy and is committed to protecting your personal information. This Privacy Policy explains how we collect, use, and safeguard your data when you use the GRS Wall Designer software and website.</p>

                <h4>1. INFORMATION WE COLLECT</h4>
                <ul>
                    <li>Account Information: Name, email, phone (if applicable), login credentials</li>
                    <li>Usage Data: Pages accessed, time stamps, IP address</li>
                    <li>Design Data: Inputs/outputs you enter in the software</li>
                </ul>

                <h4>2. HOW WE USE YOUR DATA</h4>
                <ul>
                    <li>To provide access to the Software</li>
                    <li>To improve performance and user experience</li>
                    <li>For subscription billing and authentication</li>
                    <li>To contact you for updates or support</li>
                </ul>

                <h4>3. DATA STORAGE</h4>
                <ul>
                    <li>Your data is stored on secure cloud-based servers with access control.</li>
                    <li>We do not sell or share your data with third parties except payment processors or as required by law.</li>
                </ul>

                <h4>4. COOKIES & TRACKING</h4>
                <p>We use session cookies and analytics tools to monitor system performance and user flow.</p>

                <h4>5. DATA SECURITY</h4>
                <p>We follow best practices to protect your data, including SSL encryption and regular audits.</p>

                <h4>6. YOUR RIGHTS</h4>
                <p>You have the right to access, modify, or delete your personal data by contacting <NAME_EMAIL></p>

                <h4>7. POLICY CHANGES</h4>
                <p>We may update this policy as needed. You will be notified of significant changes.</p>

                <p>Contact <NAME_EMAIL> for any privacy-related concerns.</p>


                <h3>Acknowledgement</h3>
                <p>We acknowledge the efforts of the open-source community and thank the authors and maintainers of these tools for enabling the development of this software.</p>
                <p>All trademarks and copyrights remain the property of their respective owners.</p>

                <p class="last-updated"><strong>© 2025 Geosynapse Pvt. Ltd. All rights reserved.</strong></p>
            </div>
        </div>
        
        <div class="privacy-actions">
            <form method="POST" action="{{ url_for('privacy_policy') }}">
                <div class="form-group">
                    <label class="checkbox-container">
                        <input type="checkbox" name="accept_privacy" id="accept_privacy" required>
                        <span class="checkmark"></span>
                        I have read and accept the Terms & Conditions for this session
                    </label>
                </div>
                
                <div class="button-group">
                    <button type="submit" class="btn btn-primary" id="accept-btn" disabled>
                        <i class="fas fa-check"></i>
                        Accept and Continue
                    </button>
                    <button type="button" class="btn btn-secondary" id="decline-btn" onclick="handleDecline()">
                        <i class="fas fa-times"></i>
                        Decline
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<style>
/* Override body and html to remove all white margins/padding */
body {
    margin: 0 !important;
    padding: 0 !important;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%) !important;
    overflow-x: hidden !important;
    min-height: 100vh !important;
}

html {
    margin: 0 !important;
    padding: 0 !important;
    min-height: 100vh !important;
}

/* Remove any default browser margins and padding */
* {
    box-sizing: border-box;
}

/* Privacy Policy Page Styles */
.privacy-container {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem;
    background: transparent;
    margin: 0;
    width: 100vw;
    box-sizing: border-box;
}

.privacy-card {
    background: white;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 900px;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    margin: 0;
    border: none;
}

.privacy-header {
    padding: 1.5rem;
    text-align: center;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    color: white;
    margin: 0;
    border: none;
    flex-shrink: 0;
}

.privacy-logo {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.privacy-header h2 {
    margin: 0 0 0.5rem 0;
    font-size: 1.5rem;
    font-weight: 700;
}

.privacy-header p {
    margin: 0;
    opacity: 0.9;
    font-size: 0.9rem;
    line-height: 1.4;
}

.privacy-content {
    flex: 1;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.privacy-scroll {
    flex: 1;
    overflow-y: auto;
    padding: 1.5rem;
    line-height: 1.6;
    min-height: 0;
}

.privacy-scroll h3 {
    color: var(--primary-color);
    margin-top: 1.5rem;
    margin-bottom: 0.8rem;
    font-size: 1rem;
    font-weight: 600;
}

.privacy-scroll h3:first-child {
    margin-top: 0;
}

.privacy-scroll h4 {
    color: var(--primary-color);
    margin-top: 1rem;
    margin-bottom: 0.6rem;
    font-size: 0.9rem;
    font-weight: 600;
}

.privacy-scroll p {
    margin-bottom: 0.8rem;
    color: #555;
    font-size: 0.9rem;
}

.privacy-scroll ul {
    margin-bottom: 1.2rem;
    padding-left: 1.5rem;
}

.privacy-scroll li {
    margin-bottom: 0.4rem;
    color: #666;
    font-size: 0.9rem;
}

.last-updated {
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #eee;
    font-size: 0.8rem;
    color: #777;
}

.privacy-actions {
    padding: 0.3rem 0.5rem;
    border-top: 1px solid #eee;
    background: #f8f9fa;
    flex-shrink: 0;
}

.checkbox-container {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.3rem;
    cursor: pointer;
    font-size: 0.8rem;
    color: #333;
}

.checkbox-container input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 14px;
    height: 14px;
    border: 2px solid #ddd;
    border-radius: 3px;
    margin-right: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    flex-shrink: 0;
}

.checkbox-container input[type="checkbox"]:checked + .checkmark {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.checkbox-container input[type="checkbox"]:checked + .checkmark:after {
    content: "✓";
    color: white;
    font-weight: bold;
    font-size: 12px;
}

.button-group {
    display: flex;
    gap: 0.5rem;
    justify-content: center;
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    min-width: 100px;
    justify-content: center;
}

.btn-primary {
    background: var(--primary-color);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.btn-primary:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
    text-decoration: none;
    color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
    .privacy-container {
        padding: 1rem;
        margin: 0;
    }
    
    .privacy-card {
        max-height: 95vh;
        margin: 0;
    }
    
    .privacy-header {
        padding: 1rem;
    }
    
    .privacy-scroll {
        padding: 1rem;
    }
    
    .privacy-actions {
        padding: 1rem;
    }
    
    .button-group {
        flex-direction: column;
        gap: 0.5rem;
    }
    
    .btn {
        width: 100%;
        min-width: unset;
    }
    
    body {
        margin: 0 !important;
        padding: 0 !important;
    }
    
    .privacy-header h2 {
        font-size: 1.3rem;
    }
    
    .privacy-header p {
        font-size: 0.85rem;
    }
    
    .privacy-logo {
        font-size: 1.5rem;
    }
}

/* Custom scrollbar for privacy content */
.privacy-scroll::-webkit-scrollbar {
    width: 8px;
}

.privacy-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.privacy-scroll::-webkit-scrollbar-thumb {
    background: #ccc;
    border-radius: 4px;
}

.privacy-scroll::-webkit-scrollbar-thumb:hover {
    background: #bbb;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('accept_privacy');
    const acceptBtn = document.getElementById('accept-btn');
    
    checkbox.addEventListener('change', function() {
        acceptBtn.disabled = !this.checked;
    });
    
    // Handle form submission
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        if (!checkbox.checked) {
            e.preventDefault();
            alert('Please accept the Terms & Conditions to continue.');
            return;
        }
        
        // Show loading state
        acceptBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Processing...';
        acceptBtn.disabled = true;
    });
});

function handleDecline() {
    // Show popup immediately when decline is clicked
    const confirmed = confirm(
        'You are about to decline our Terms & Conditions.\n\n' +
        'This means:\n' +
        '• You will be logged out immediately\n' +
        '• You will not be able to use the GRS Wall Designer application\n' +
        '• You must accept the Terms & Conditions to continue using our services\n\n' +
        'Are you sure you want to decline?'
    );
    
    if (confirmed) {
        // If user confirms, redirect to decline route
        window.location.href = '{{ url_for("decline_privacy") }}';
    }
    // If user cancels, nothing happens - they stay on the privacy policy page
}
</script>

{% endblock %}
