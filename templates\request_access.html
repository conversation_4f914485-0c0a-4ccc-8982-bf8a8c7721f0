{% extends "base.html" %} {% block auth_content %}

<div class="login-container"><div class="login-card" style="max-width: 600px"><div class="login-header"><div class="login-logo"><i class="fas fa-user-plus"></i></div><h2>Request Access</h2><p>
  Fill out the form below to request access to the GRS Wall Designer
  application.
  </p></div><div class="login-form"><form method="POST" action="{{ url_for('request_access') }}"><div class="form-group"><label for="full_name">Full Name*</label><input
    type="text"
    class="form-control"
    name="full_name"
    required
    placeholder="Enter your full name"
    /></div><div class="form-group"><label for="email">Email Address*</label><input
    type="email"
    class="form-control"
    name="email"
    required
    placeholder="Enter your email address"
    /><small class="form-text text-muted">
    We'll send your credentials to this address if approved.
    </small></div><div class="form-group"><label for="organization">Organization/Institution</label><input
    type="text"
    class="form-control"
    name="organization"
    placeholder="Enter your organization name"
    /></div><div class="form-group"><label for="designation">Designation*</label><select
    class="form-control"
    name="designation"
    id="designation"
    required
    onchange="toggleCustomDesignation()"
    >
    <option value="">Select your designation</option>
    <option value="engineer">Engineer</option>
    <option value="student">Student</option>
    <option value="professor">Professor</option>
    <option value="other">Other</option>
    </select></div><div class="form-group" id="custom-designation-group" style="display: none;"><label for="custom_designation">Please specify your designation*</label><input
    type="text"
    class="form-control"
    name="custom_designation"
    id="custom_designation"
    placeholder="Enter your designation"
    /></div><div class="form-group"><label for="purpose">Purpose for Access*</label><textarea
    class="form-control"
    name="purpose"
    rows="4"
    required
    placeholder="Please describe how you intend to use this application..."
    ></textarea></div><button type="submit" class="btn btn-primary btn-login"><i class="fas fa-paper-plane"></i>
    Submit Request
  </button></form><div class="login-footer"><p>Already have an account?</p><a href="{{ url_for('login') }}" class="btn btn-outline-primary"><i class="fas fa-arrow-left"></i>
    Back to Login
  </a></div></div></div></div><style>
  /* Override any conflicting body styles for request access page */
  body {
  margin: 0 !important;
  padding: 0 !important;
  background: none !important;
  }

  html {
  margin: 0 !important;
  padding: 0 !important;
  }

  /* Ensure auth container fills entire viewport */
  .auth-container {
  min-height: 100vh !important;
  width: 100vw !important;
  margin: 0 !important;
  padding: 0 !important;
  background: linear-gradient(
  135deg,
  var(--primary-color) 0%,
  var(--primary-dark) 100%
  ) !important;
  overflow-x: hidden !important;
  }

  /* Modern Request Access Page Styles */
  .login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 2rem;
  width: 100%;
  box-sizing: border-box;
  }

  /* Fix text color - make header text white for better contrast on blue background */
  .login-header h2 {
  color: white !important;
  }

  .login-header p {
  color: rgba(255, 255, 255, 0.9) !important;
  }

  /* Form labels - make them white for better contrast on blue background */
  .form-group label {
  color: white !important;
  font-weight: 600;
  margin-bottom: 0.5rem;
  display: block;
  }

  /* Form helper text - make it light gray for visibility */
  .form-text,
  .text-muted {
  font-size: 0.75rem;
  color: rgba(255, 255, 255, 0.8) !important;
  margin-top: 0.25rem;
  display: block;
  }

  /* Login footer text - make it white */
  .login-footer p {
  color: white !important;
  margin-bottom: 1rem;
  }

  /* Outline button styling for "Back to Login" */
  .btn-outline-primary {
  background-color: transparent !important;
  border: 2px solid white !important;
  color: white !important;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  font-weight: 600;
  font-size: 0.875rem;
  transition: all var(--transition-fast);
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  min-height: 44px;
  box-sizing: border-box;
  }

  .btn-outline-primary:hover {
  background-color: white !important;
  color: var(--primary-color) !important;
  border-color: white !important;
  text-decoration: none;
  }

  /* Form control styling for consistency */
  .form-control {
  width: 100% !important;
  padding: 0.75rem !important;
  border: 2px solid #d1d5db !important;
  border-radius: 8px !important;
  background-color: white !important;
  color: #333 !important;
  font-size: 1rem !important;
  box-sizing: border-box !important;
  transition: all 0.2s ease !important;
  }

  .form-control:focus {
  outline: none !important;
  border-color: #2563eb !important;
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1) !important;
  }

  /* Specific styling for text areas */
  textarea.form-control {
  resize: vertical !important;
  min-height: 100px !important;
  }

  /* Specific styling for select dropdowns */
  select.form-control {
  appearance: none !important;
  background-image: url("data:image/svg+xml;charset=US-ASCII,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 5'><path fill='%23666' d='M2 0L0 2h4zm0 5L0 3h4z'/></svg>") !important;
  background-repeat: no-repeat !important;
  background-position: right 0.75rem center !important;
  background-size: 12px !important;
  cursor: pointer !important;
  }

  #custom-designation-group {
  margin-top: 1rem;
  transition: all 0.3s ease;
  }
</style><script>
  function toggleCustomDesignation() {
    const designation = document.getElementById('designation').value;
    const customGroup = document.getElementById('custom-designation-group');
    const customInput = document.getElementById('custom_designation');
    
    if (designation === 'other') {
      customGroup.style.display = 'block';
      customInput.required = true;
    } else {
      customGroup.style.display = 'none';
      customInput.required = false;
      customInput.value = '';
    }
  }

  document.addEventListener("DOMContentLoaded", function () {
  const form = document.querySelector("form");
  const submitButton = form.querySelector('button[type="submit"]');

  form.addEventListener("submit", function (e) {
  e.preventDefault(); // Prevent default form submission

  // Validate custom designation if 'other' is selected
  const designation = form.designation.value;
  const customDesignation = form.custom_designation.value.trim();
  
  if (designation === 'other' && !customDesignation) {
    alert('Please specify your designation when selecting "Other".');
    return;
  }

  // Get form data
  const formData = {
  full_name: form.full_name.value,
  email: form.email.value,
  organization: form.organization.value,
  designation: designation,
  custom_designation: customDesignation,
  purpose: form.purpose.value,
  };

  // Disable submit button and show loading state
  const originalText = submitButton.innerHTML;
  submitButton.innerHTML =
  '<i class="fas fa-spinner fa-spin"></i> Submitting...';
  submitButton.disabled = true;

  // Send AJAX request
  fetch("/request_access", {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify(formData),
  })
  .then((response) => response.json())
  .then((data) => {
    // Show popup based on response
    if (data.status === "success") {
    alert(
    "Thank you for your request!\n\nWe will contact you soon regarding your access request."
    );
    // Redirect to login page after user clicks OK
    window.location.href = "/login";
    } else if (data.status === "info") {
    alert(data.message);
    // Redirect to login page
    window.location.href = "/login";
    } else {
    alert("Error: " + data.message);
    // Re-enable the submit button
    submitButton.innerHTML = originalText;
    submitButton.disabled = false;
    }
  })
  .catch((error) => {
    console.error("Error:", error);
    alert(
    "An error occurred while submitting your request. Please try again."
    );
    // Re-enable the submit button
    submitButton.innerHTML = originalText;
    submitButton.disabled = false;
  });
  });
  });
</script>

{% endblock %}