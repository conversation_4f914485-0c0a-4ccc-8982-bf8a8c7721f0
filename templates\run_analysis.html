{% extends "base.html" %}
{% block page_title %}Run Analysis - GRS Wall Designer{% endblock %}
{% block content %}
<div class="run-analysis-container"><div class="analysis-header"><h1>Run Analysis</h1><p class="analysis-description">
  Execute comprehensive analysis of your GRS wall design based on IS 18591:2024 standards.
  Ensure all required data has been entered and saved before running the analysis.
  </p></div><div class="analysis-workflow"><div class="workflow-step"><div class="step-icon"><i class="fas fa-check-circle"></i></div><div class="step-content"><h3>Data Validation</h3><p>Verify that all required project data has been entered and saved.</p></div></div><div class="workflow-step"><div class="step-icon"><i class="fas fa-calculator"></i></div><div class="step-content"><h3>Analysis Execution</h3><p>Perform external and internal stability calculations according to IS 18591:2024.</p></div></div><div class="workflow-step"><div class="step-icon"><i class="fas fa-chart-line"></i></div><div class="step-content"><h3>Results Generation</h3><p>Generate comprehensive analysis results and safety factor calculations.</p></div></div></div><div class="analysis-section"><div class="analysis-card"><h2>Ready to Run Analysis?</h2><p>
  After saving all inputs from the previous sections, click below to run
  the comprehensive analysis of your GRS wall design.
  </p>
  {% if has_existing_results %}
  <div class="alert alert-success mb-3">
    <i class="fas fa-check-circle"></i>
    <strong>Analysis Complete!</strong> Your previous analysis results are available.
  </div>
  <button id="show-results-btn" class="cta-button me-3">
    <i class="fas fa-chart-line"></i> Show Results
  </button>
  <button id="run-analysis-btn" class="cta-button">
    <i class="fas fa-sync-alt"></i> Re-run Analysis
  </button>
  {% else %}
  <button id="run-analysis-btn" class="cta-button">
    <i class="fas fa-play-circle"></i> Run Analysis
  </button>
  {% endif %}</div></div></div>

<!-- Analysis Results Modal -->
<div id="analysisModal" class="modal fade" tabindex="-1" aria-labelledby="analysisModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header" style="background: linear-gradient(135deg, #28a745, #20692f); color: white;">
        <h5 class="modal-title" id="analysisModalLabel">
          <i class="fas fa-check-circle"></i> Analysis Results
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close" style="filter: invert(1);"></button>
      </div>
      <div class="modal-body">
        <!-- Analysis Section -->
        <div id="analysisSection">
          <div class="analysis-workflow mb-4">
            <div class="row">
              <div class="col-md-4 mb-3">
                <div class="workflow-step text-center" style="padding: 1rem; border-radius: 8px; background: #f8f9fa; height: 100%;">
                  <div class="step-icon mb-2">
                    <i class="fas fa-check-circle text-success" style="font-size: 2rem;"></i>
                  </div>
                  <h6 style="color: #495057; font-weight: 600; margin-bottom: 0.25rem;">Data Validation</h6>
                  <small class="text-muted">Verify required project data</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="workflow-step text-center" style="padding: 1rem; border-radius: 8px; background: #f8f9fa; height: 100%;">
                  <div class="step-icon mb-2">
                    <i class="fas fa-calculator text-primary" style="font-size: 2rem;"></i>
                  </div>
                  <h6 style="color: #495057; font-weight: 600; margin-bottom: 0.25rem;">Analysis Execution</h6>
                  <small class="text-muted">Perform stability calculations</small>
                </div>
              </div>
              <div class="col-md-4 mb-3">
                <div class="workflow-step text-center" style="padding: 1rem; border-radius: 8px; background: #f8f9fa; height: 100%;">
                  <div class="step-icon mb-2">
                    <i class="fas fa-chart-line text-info" style="font-size: 2rem;"></i>
                  </div>
                  <h6 style="color: #495057; font-weight: 600; margin-bottom: 0.25rem;">Results Generation</h6>
                  <small class="text-muted">Generate safety factors</small>
                </div>
              </div>
            </div>
          </div>
          
          <div class="text-center">
            <button id="modalRunAnalysisBtn" class="btn btn-primary btn-lg">
              <i class="fas fa-play-circle"></i> Run Analysis
            </button>
          </div>
        </div>

        <!-- Results Section (hidden initially) -->
        <div id="resultsSection" style="display: none;">
          <div class="alert alert-success mb-4">
            <i class="fas fa-check-circle"></i>
            <strong>Analysis Complete!</strong> Your GRS wall design has been analyzed successfully.
          </div>
          
          <div class="row mb-4">
            <div class="col-md-6 mb-3">
              <button type="button" class="btn btn-outline-primary btn-lg w-100 results-link" data-url="{{ url_for('external_stability_results') }}" style="transition: all 0.3s ease; border: 2px solid transparent;">
                <i class="fas fa-external-link-alt"></i>
                External Stability Results
              </button>
            </div>
            <div class="col-md-6 mb-3">
              <button type="button" class="btn btn-outline-primary btn-lg w-100 results-link" data-url="{{ url_for('internal_stability_results') }}" style="transition: all 0.3s ease; border: 2px solid transparent;">
                <i class="fas fa-cogs"></i>
                Internal Stability Results
              </button>
            </div>
          </div>

          <div class="satisfaction-section mb-4">
            <div class="card bg-light" style="border: 2px solid #e9ecef; transition: border-color 0.3s ease;">
              <div class="card-body">
                <div class="form-check">
                  <input type="checkbox" id="modalSatisfactionCheckbox" class="form-check-input" autocomplete="off">
                  <label for="modalSatisfactionCheckbox" class="form-check-label">
                    <strong>Are you satisfied with the design configuration?</strong>
                  </label>
                </div>
              </div>
            </div>
          </div>

          <div id="modalGenerateReportContainer" style="display: none;">
            <div class="alert alert-info mb-3">
              <strong>Note:</strong> For the best report quality, please take a screenshot from the
              <a href="/reinforcementlayout" target="_blank">Reinforcement Layout</a> section before generating the report.
            </div>
            <div class="text-center">
              <button id="modalGenerateReportBtn" class="btn btn-success btn-lg">
                <i class="fas fa-file-pdf"></i> Generate Report
              </button>
            </div>
          </div>
        </div>

        <!-- Error Section (hidden initially) -->
        <div id="modalErrorSection" class="alert alert-danger" style="display: none;">
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  // ===== UTILITY FUNCTIONS =====
  
  // Comprehensive modal cleanup function
  function cleanupModalState() {
    // Remove any modal backdrops
    const existingBackdrops = document.querySelectorAll('.modal-backdrop');
    existingBackdrops.forEach(backdrop => backdrop.remove());
    
    // Reset body classes and styles
    document.body.classList.remove('modal-open');
    document.body.style.overflow = '';
    document.body.style.paddingRight = '';
  }

  document.addEventListener("DOMContentLoaded", function () {
    // Clean up any leftover modal state from previous navigation
    cleanupModalState();
    
    // Prevent multiple initializations
    if (window.analysisPageInitialized) {
      return;
    }
    window.analysisPageInitialized = true;
    
    const runAnalysisBtn = document.getElementById("run-analysis-btn");
    const showResultsBtn = document.getElementById("show-results-btn");

    // Add event handler for "Show Results" button
    if (showResultsBtn) {
      showResultsBtn.addEventListener("click", function() {
        showAnalysisResultsPopup(false); // false = showing previous results
      });
    }

    // Add event handler for "Run Analysis" button
    if (runAnalysisBtn) {
      runAnalysisBtn.addEventListener("click", function () {
        // Prevent multiple clicks
        if (runAnalysisBtn.disabled) return;
        
        // Disable button to prevent multiple clicks
        runAnalysisBtn.disabled = true;
        
        // Run analysis directly and then show popup with results
        fetch('/run_analysis', { method: 'POST' })
          .then(response => response.json())
          .then(data => {
            if (data.has_results) {
              // Show popup with results directly (true = fresh analysis)
              showAnalysisResultsPopup(true);
            } else {
              // Show error popup
              showErrorPopup(data.error || 'Analysis failed. Please check your input data.');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            showErrorPopup('An error occurred while running the analysis. Please try again.');
          })
          .finally(() => {
            // Reset button
            runAnalysisBtn.innerHTML = '<i class="fas fa-sync-alt"></i> Re-run Analysis';
            runAnalysisBtn.disabled = false;
          });
      });
    }

    // ===== MAIN FUNCTIONS =====
    
    // Function to show analysis results popup with different messages
    function showAnalysisResultsPopup(isNewAnalysis = false) {
      // Show results section directly
      document.getElementById('analysisSection').style.display = 'none';
      document.getElementById('resultsSection').style.display = 'block';
      document.getElementById('modalErrorSection').style.display = 'none';
      
      // Update the success message based on action
      const successAlert = document.querySelector('#resultsSection .alert-success');
      if (successAlert) {
        if (isNewAnalysis) {
          successAlert.innerHTML = `
            <i class="fas fa-check-circle"></i>
            <strong>Analysis Re-run Complete!</strong> Your GRS wall design has been analyzed with the latest data.
          `;
        } else {
          successAlert.innerHTML = `
            <i class="fas fa-chart-line"></i>
            <strong>Showing Previous Results!</strong> Your previous analysis results are displayed below.
          `;
        }
      }
      
      // Reset satisfaction checkbox
      const modalCheckbox = document.getElementById('modalSatisfactionCheckbox');
      if (modalCheckbox) {
        modalCheckbox.checked = false;
      }
      const modalGenerateReportContainer = document.getElementById('modalGenerateReportContainer');
      if (modalGenerateReportContainer) {
        modalGenerateReportContainer.style.display = 'none';
      }
      
      // Clean up any existing modal backdrops before showing
      cleanupModalState();
      
      // Show modal with proper cleanup
      const modalElement = document.getElementById('analysisModal');
      const modal = new bootstrap.Modal(modalElement, {
        backdrop: true,
        keyboard: true
      });
      
      // Add event listener for proper cleanup when modal is hidden
      modalElement.addEventListener('hidden.bs.modal', cleanupModalState, { once: true });
      
      modal.show();
    }

    // Function to show error popup
    function showErrorPopup(errorMessage) {
      // Clean up any existing modal backdrops
      cleanupModalState();
      
      // Create and show error modal
      const errorModal = document.createElement('div');
      errorModal.className = 'modal fade';
      errorModal.id = 'errorModal';
      errorModal.innerHTML = `
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header bg-danger text-white">
              <h5 class="modal-title">
                <i class="fas fa-exclamation-triangle"></i> Analysis Error
              </h5>
              <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
              <div class="alert alert-danger mb-0">
                <strong>Error:</strong> ${errorMessage}
              </div>
              <p class="mt-3 mb-0">
                <small class="text-muted">
                  Please check your input data and ensure all required fields are filled correctly.
                </small>
              </p>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
          </div>
        </div>
      `;
      
      document.body.appendChild(errorModal);
      const modal = new bootstrap.Modal(errorModal, {
        backdrop: true,
        keyboard: true
      });
      
      // Add event listener for proper cleanup when modal is hidden
      errorModal.addEventListener('hidden.bs.modal', function() {
        cleanupModalState();
        // Remove modal from DOM
        document.body.removeChild(errorModal);
      }, { once: true });
      
      modal.show();
    }

    // ===== EVENT HANDLERS =====
    
    // Modal satisfaction checkbox handler
    const modalSatisfactionCheckbox = document.getElementById('modalSatisfactionCheckbox');
    if (modalSatisfactionCheckbox) {
      modalSatisfactionCheckbox.addEventListener('change', function() {
        const modalGenerateReportContainer = document.getElementById('modalGenerateReportContainer');
        if (modalGenerateReportContainer) {
          modalGenerateReportContainer.style.display = this.checked ? 'block' : 'none';
        }
      });
    }

    // Modal generate report button handler
    const modalGenerateReportBtn = document.getElementById('modalGenerateReportBtn');
    if (modalGenerateReportBtn) {
      modalGenerateReportBtn.addEventListener('click', function() {
        window.open('/generate_report', '_blank');
      });
    }

    // Modal run analysis button handler (in the modal)
    const modalRunAnalysisBtn = document.getElementById('modalRunAnalysisBtn');
    if (modalRunAnalysisBtn) {
      modalRunAnalysisBtn.addEventListener('click', function() {
        // Prevent multiple clicks
        if (modalRunAnalysisBtn.disabled) return;
        
        // Show spinner and disable button
        const originalText = modalRunAnalysisBtn.innerHTML;
        modalRunAnalysisBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Running Analysis...';
        modalRunAnalysisBtn.disabled = true;
        
        // Run analysis
        fetch('/run_analysis', { method: 'POST' })
          .then(response => response.json())
          .then(data => {
            if (data.has_results) {
              // Show results section directly
              showAnalysisResultsPopup(true);
            } else {
              // Show error popup
              showErrorPopup(data.error || 'Analysis failed. Please check your input data.');
            }
          })
          .catch(error => {
            console.error('Error:', error);
            showErrorPopup('An error occurred while running the analysis. Please try again.');
          })
          .finally(() => {
            // Reset button
            modalRunAnalysisBtn.innerHTML = originalText;
            modalRunAnalysisBtn.disabled = false;
          });
      });
    }

    // ===== AJAX NAVIGATION SETUP =====
    
    // Add AJAX navigation to results links
    const resultsLinks = document.querySelectorAll('.results-link');
    resultsLinks.forEach(link => {
      link.addEventListener('click', function(e) {
        e.preventDefault();
        const url = this.getAttribute('data-url');
        
        // Show loading spinner
        if (window.showLoadingScreen) {
          window.showLoadingScreen();
        }
        
        // Load content via AJAX
        fetch(url)
          .then(response => response.text())
          .then(html => {
            // Parse the HTML to extract just the content
            const parser = new DOMParser();
            const doc = parser.parseFromString(html, 'text/html');
            const content = doc.querySelector('.content-area');
            
            // Hide the modal first
            const modal = bootstrap.Modal.getInstance(document.getElementById('analysisModal'));
            if (modal) {
              modal.hide();
              
              // Wait for modal to be fully hidden before proceeding
              document.getElementById('analysisModal').addEventListener('hidden.bs.modal', function() {
                // Clean up modal state
                cleanupModalState();
                
                // Replace the main content with the results page content
                const mainContent = document.querySelector('.content-area');
                if (mainContent && content) {
                  mainContent.innerHTML = content.innerHTML;
                  
                  // Add a back button to return to analysis page
                  const backButtonHtml = `
                    <div class="mt-4 mb-4">
                      <button class="btn btn-secondary ajax-back-btn">
                        <i class="fas fa-arrow-left"></i> Back to Analysis
                      </button>
                    </div>
                  `;
                  mainContent.insertAdjacentHTML('afterbegin', backButtonHtml);
                  
                  // Add event listener to the new back button
                  const backBtn = mainContent.querySelector('.ajax-back-btn');
                  if (backBtn) {
                    backBtn.addEventListener('click', function() {
                      // Show loading spinner
                      if (window.showLoadingScreen) {
                        window.showLoadingScreen();
                      }
                      
                      // Load the analysis page via AJAX instead of reload
                      fetch('/run_analysis_page')
                        .then(response => response.text())
                        .then(html => {
                          const parser = new DOMParser();
                          const doc = parser.parseFromString(html, 'text/html');
                          const content = doc.querySelector('.content-area');
                          
                          if (mainContent && content) {
                            mainContent.innerHTML = content.innerHTML;
                            
                            // Reinitialize the analysis page by triggering DOMContentLoaded
                            setTimeout(() => {
                              // Reset the initialization flag first
                              window.analysisPageInitialized = false;
                              
                              // Trigger DOMContentLoaded event for the new content
                              const event = new Event('DOMContentLoaded', {
                                bubbles: true,
                                cancelable: true
                              });
                              document.dispatchEvent(event);
                            }, 100);
                          }
                          
                          if (window.hideLoadingScreen) {
                            window.hideLoadingScreen();
                          }
                        })
                        .catch(error => {
                          console.error('Error loading analysis page:', error);
                          if (window.hideLoadingScreen) {
                            window.hideLoadingScreen();
                          }
                          // Fallback to reload only if AJAX fails
                          window.location.reload();
                        });
                    });
                  }
                }
                
                // Hide loading spinner
                if (window.hideLoadingScreen) {
                  window.hideLoadingScreen();
                }
              }, { once: true });
            } else {
              // No modal instance, proceed immediately
              // Replace the main content with the results page content
              const mainContent = document.querySelector('.content-area');
              if (mainContent && content) {
                mainContent.innerHTML = content.innerHTML;
                
                // Add a back button to return to analysis page
                const backButtonHtml = `
                  <div class="mt-4 mb-4">
                    <button class="btn btn-secondary ajax-back-btn">
                      <i class="fas fa-arrow-left"></i> Back to Analysis
                    </button>
                  </div>
                `;
                mainContent.insertAdjacentHTML('afterbegin', backButtonHtml);
                
                // Add event listener to the new back button
                const backBtn = mainContent.querySelector('.ajax-back-btn');
                if (backBtn) {
                  backBtn.addEventListener('click', function() {
                    // Show loading spinner
                    if (window.showLoadingScreen) {
                      window.showLoadingScreen();
                    }
                    
                    // Load the analysis page via AJAX instead of reload
                    fetch('/run_analysis_page')
                      .then(response => response.text())
                      .then(html => {
                        const parser = new DOMParser();
                        const doc = parser.parseFromString(html, 'text/html');
                        const content = doc.querySelector('.content-area');
                        
                        if (mainContent && content) {
                          mainContent.innerHTML = content.innerHTML;
                          
                          // Reinitialize the analysis page by triggering DOMContentLoaded
                          setTimeout(() => {
                            // Reset the initialization flag first
                            window.analysisPageInitialized = false;
                            
                            // Trigger DOMContentLoaded event for the new content
                            const event = new Event('DOMContentLoaded', {
                              bubbles: true,
                              cancelable: true
                            });
                            document.dispatchEvent(event);
                          }, 100);
                        }
                        
                        if (window.hideLoadingScreen) {
                          window.hideLoadingScreen();
                        }
                      })
                      .catch(error => {
                        console.error('Error loading analysis page:', error);
                        if (window.hideLoadingScreen) {
                          window.hideLoadingScreen();
                        }
                        // Fallback to reload only if AJAX fails
                        window.location.reload();
                      });
                  });
                }
              }
              
              // Hide loading spinner
              if (window.hideLoadingScreen) {
                window.hideLoadingScreen();
              }
            }
          })
          .catch(error => {
            console.error('Error loading results:', error);
            // Hide loading spinner
            if (window.hideLoadingScreen) {
              window.hideLoadingScreen();
            }
            // Fall back to regular navigation
            window.location.href = url;
          });
      });
    });

    // ===== GLOBAL FUNCTIONS =====
    
    // Make function globally available for template use
    window.showAnalysisResultsPopup = showAnalysisResultsPopup;
    
    // Global reset function for AJAX reinitialization
    window.resetAnalysisPage = function() {
      window.analysisPageInitialized = false;
    };
  });
</script>

{% endblock %}