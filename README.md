# GRS Wall Designer

A web-based application for designing Geosynthetic Reinforced Soil (GRS) walls with comprehensive analysis and reporting capabilities.

**Optimized for 2-core, 1GB RAM systems with full potential utilization.**

## 📁 Project Structure (Clean & Optimized)

```
flask workspace2/
├── README.md              # This comprehensive guide
├── app.py                 # Main Flask application (2,489 lines)
├── config.py              # ALL configuration settings (76 lines, credentials included)
├── backend.py             # Calculation engine and analysis logic
├── wsgi.py                # WSGI entry point for Gunicorn (16 lines, clean)
├── gunicorn.conf.py       # Production server config (30 lines, 2-core optimized)
├── run_production.py      # Production launcher (130+ lines, uses Gunicorn WSGI)
├── maintenance.html       # Fallback maintenance page (professional UI)
├── requirements.txt       # Essential dependencies only (15 lines)
├── templates/             # HTML templates
├── static/                # CSS, JavaScript, and static files
└── logs/                  # Application logs
```

**✅ All redundant files removed - No .env, .bat, .sh scripts, or extra documentation**

## 🚀 Quick Start

### For Beginners (Development Mode)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Run the application:**
   ```bash
   python app.py
   ```

3. **Open your browser:**
   ```
   http://localhost:5000
   ```

### For Production Deployment (Linux Server - Recommended)

1. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Start production server with Gunicorn:**
   ```bash
   python run_production.py
   ```

3. **Open your browser:**
   ```
   http://localhost:8000
   ```

**That's it!** All credentials are in `config.py` - no environment variables needed.

**✅ Optimized for Linux servers with automatic fallback if server fails to start**

## 📋 File Descriptions

### Core Application Files

- **`app.py`** - Main Flask application with all routes, database operations, and business logic
- **`config.py`** - ALL configuration settings including database credentials (no env vars needed)
- **`backend.py`** - Engineering calculations and analysis algorithms for GRS wall design

### Production Files

- **`wsgi.py`** - WSGI entry point for Gunicorn production server (with startup logging)
- **`gunicorn.conf.py`** - Optimized for 2-core, 1GB RAM system (3 workers, memory-efficient)
- **`run_production.py`** - Production launcher that uses Gunicorn WSGI server
- **`requirements.txt`** - Python dependencies including Gunicorn

## 🔧 Configuration

### Simple Configuration

All settings are in `config.py` - **no environment variables needed!**

**Database Settings (in config.py):**
```python
MYSQL_HOST = 'localhost'
MYSQL_USER = 'root'
MYSQL_PASSWORD = 'sriroot'     # Change this to your MySQL password
MYSQL_DB = 'grs_software'
```

**To change database password:** Edit `config.py` and update `MYSQL_PASSWORD` in both `DevelopmentConfig` and `ProductionConfig` classes.

### Development vs Production (System Optimized)

| Feature | Development | Production |
|---------|-------------|------------|
| Debug Mode | ✅ ON | ❌ OFF |
| Console Logs | ✅ YES | ❌ NO |
| Log Level | INFO | WARNING |
| Server | Flask Dev | **Gunicorn WSGI** |
| Workers | 1 | **3 (2-core optimized)** |
| DB Pool | 5 connections | **8 connections** |
| Memory | Normal | **Optimized for 1GB RAM** |
| Port | 5000 | 8000 |

## 🏃‍♂️ How to Run

### Method 1: Development (For testing and development)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Run application
python app.py

# 3. Open browser to http://localhost:5000
```

### Method 2: Production with Gunicorn (Recommended for real use)
```bash
# 1. Install dependencies
pip install -r requirements.txt

# 2. Start production server (uses Gunicorn WSGI)
python run_production.py

# 3. Open browser to http://localhost:8000
```

**Production server features:**
- ✅ **3 worker processes** (optimized for 2-core system)
- ✅ **Memory efficient** (designed for 1GB RAM)
- ✅ **Auto-restart workers** (prevents memory leaks)
- ✅ **Production logging** (errors only)
- ✅ **Better performance** than Flask dev server
- ✅ **Automatic fallback** (shows maintenance page if server fails)

## 🗄️ Database Setup

The application requires a MySQL database. Make sure you have:

1. **MySQL server running**
2. **Database created:** `CREATE DATABASE grs_software;`
3. **User with permissions:** The app will create tables automatically on first run
4. **Environment variables set** with correct database credentials

## 📊 Logging

Logs are written to the `logs/` directory:

- **`logs/grs_app.log`** - Main application logs
- **`logs/grs_summary.log`** - Critical events and login attempts  
- **`logs/{username}.log`** - User-specific activity logs
- **`logs/gunicorn_access.log`** - Production server access logs (when using Gunicorn)
- **`logs/gunicorn_error.log`** - Production server error logs (when using Gunicorn)

## 🔒 Security Notes

- **No hardcoded credentials** - All sensitive data comes from environment variables
- **Environment validation** - App won't start without required variables
- **Production-ready sessions** - HTTPS-compatible session cookies in production mode
- **Secure logging** - Sensitive data not logged in production

## 🛡️ Automatic Fallback System

The production launcher has a **3-tier fallback system**:

1. **Primary**: Gunicorn WSGI server (best performance)
2. **Fallback 1**: Flask development server (if Gunicorn fails)
3. **Fallback 2**: Maintenance page (if everything fails)

### Maintenance Mode Features:
- ✅ **Automatic activation** when server fails to start
- ✅ **Professional maintenance page** with contact information
- ✅ **Auto-refresh every 2 minutes** to check if server is back
- ✅ **Contact email**: <EMAIL>
- ✅ **Error timestamp** for debugging
- ✅ **User-friendly interface** with retry options

## 🆘 Troubleshooting

### Database connection failed
**Solution:** Check that MySQL is running and update credentials in `config.py`

### Port already in use
**Solution:** Change the port in `config.py` or stop the conflicting service

### Permission denied on logs directory
**Solution:** Make sure the application has write permissions to create the `logs/` directory

### Server won't start
**Solution:** The system will automatically show a maintenance page with contact information. Check `logs/grs_app.log` for specific errors.

### Gunicorn not working
**Solution:** Make sure you're running on Linux. Gunicorn doesn't work on Windows (use development mode instead).

### Testing maintenance mode
**Solution:** To test the maintenance page, temporarily rename `app.py` to `app.py.bak` and run `python run_production.py`. The maintenance page will automatically appear.

## 🎯 For New Users

1. **Start with development mode** - It's easier and shows more information
2. **Use the .env file approach** - Copy `.env.example` to `.env` and edit it
3. **Check the logs** - If something goes wrong, check `logs/grs_app.log`
4. **Only use production mode** when you're ready to deploy for real users

## 📞 Support

If you encounter issues:
1. Check the logs in the `logs/` directory
2. Verify all environment variables are set correctly
3. Ensure MySQL database is running and accessible
4. Make sure all dependencies are installed with `pip install -r requirements.txt`
