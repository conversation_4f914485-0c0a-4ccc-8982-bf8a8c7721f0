#!/usr/bin/env python3
"""
Production Launcher - Uses Gunicorn WSGI Server
Optimized for 2-core, 1GB RAM system
"""

import os
import sys
import subprocess
import http.server
import socketserver

def check_gunicorn():
    """Check if Gun<PERSON> is installed"""
    try:
        import gunicorn
        return True
    except ImportError:
        return False

def install_gunicorn():
    """Install Gunicorn if not present"""
    print("📦 Installing Gunicorn...")
    try:
        subprocess.run([sys.executable, '-m', 'pip', 'install', 'gunicorn'], check=True)
        print("✅ Gunicorn installed successfully")
        return True
    except subprocess.CalledProcessError:
        print("❌ Failed to install Gun<PERSON>")
        return False

def start_maintenance_server(port=5000):
    """Start a simple maintenance server if main app fails"""
    class MaintenanceHandler(http.server.SimpleHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/' or self.path == '/index.html':
                self.send_response(503)  # Service Unavailable
                self.send_header('Content-type', 'text/html')
                self.send_header('Retry-After', '120')  # Retry after 2 minutes
                self.end_headers()

                try:
                    with open('maintenance.html', 'r', encoding='utf-8') as f:
                        content = f.read()
                    self.wfile.write(content.encode('utf-8'))
                except FileNotFoundError:
                    # Fallback if maintenance.html is missing
                    fallback_html = """
                    <!DOCTYPE html>
                    <html><head><title>Server Down</title></head>
                    <body style="font-family: Arial; text-align: center; padding: 50px;">
                    <h1>🔧 Server Maintenance</h1>
                    <p>The GRS Wall Designer is currently unavailable.</p>
                    <p>Please contact: <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p><button onclick="location.reload()">Try Again</button></p>
                    </body></html>
                    """
                    self.wfile.write(fallback_html.encode('utf-8'))
            else:
                self.send_error(503, "Service Unavailable")

        def log_message(self, format, *args):
            # Suppress log messages
            pass

    try:
        with socketserver.TCPServer(("", port), MaintenanceHandler) as httpd:
            print(f"🚨 MAINTENANCE MODE: Serving fallback page on port {port}")
            print(f"📧 Users will see contact: <EMAIL>")
            print("🔄 Page auto-refreshes every 2 minutes")
            print("⏹️  Press Ctrl+C to stop")
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Maintenance server stopped")
    except Exception as e:
        print(f"❌ Could not start maintenance server: {e}")
        print("💡 Try running on a different port")

def main():
    """Main production launcher"""
    print("🚀 GRS Wall Designer Production Launcher")
    print("💻 System: 2-core, 1GB RAM optimized")

    # Force production environment
    os.environ['FLASK_ENV'] = 'production'

    # Check and install Gunicorn if needed
    if not check_gunicorn():
        if not install_gunicorn():
            print("❌ Cannot proceed without Gunicorn")
            return 1

    # Create logs directory
    os.makedirs('logs', exist_ok=True)

    print("🌐 Starting Gunicorn WSGI server...")
    print("   - Workers: 3 (optimized for 2-core system)")
    print("   - Port: 5000")
    print("   - Memory: Optimized for 1GB RAM")
    print("   - Access: http://0.0.0.0:5000 (accepts external connections)")

    # Start Gunicorn with optimized settings
    try:
        subprocess.run([
            'gunicorn',
            '--config', 'gunicorn.conf.py',
            'wsgi:application'
        ], check=True)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except subprocess.CalledProcessError as e:
        print(f"❌ Gunicorn failed: {e}")
        print("💡 Fallback 1: Trying Flask dev server...")

        # Fallback to Flask dev server
        try:
            subprocess.run([sys.executable, 'app.py'], check=True)
        except (subprocess.CalledProcessError, KeyboardInterrupt):
            print("\n❌ Flask dev server also failed")
            print("🚨 Starting maintenance mode...")
            start_maintenance_server(5000)
    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        print("🚨 Starting maintenance mode as last resort...")
        start_maintenance_server(5000)
        return 1

    return 0

if __name__ == '__main__':
    sys.exit(main())
