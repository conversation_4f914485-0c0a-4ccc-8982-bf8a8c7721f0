"""
Gunicorn Configuration - Optimized for 2-core, 1GB RAM system
"""

# Server
bind = "0.0.0.0:8000"
backlog = 512

# Workers - Optimized for 2-core, 1GB RAM system
workers = 3  # 2 cores + 1
worker_class = "sync"
worker_connections = 100
timeout = 30
keepalive = 2

# Memory management - Prevent memory leaks
max_requests = 500
max_requests_jitter = 50

# Logging
accesslog = "logs/gunicorn_access.log"
errorlog = "logs/gunicorn_error.log"
loglevel = "warning"

# Process
proc_name = 'grs_wall_designer'
pidfile = 'logs/gunicorn.pid'

# Performance
preload_app = True
