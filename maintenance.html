<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GRS Wall Designer - Server Maintenance</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }
        
        .maintenance-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 60px 40px;
            text-align: center;
            max-width: 600px;
            width: 90%;
            margin: 20px;
        }
        
        .maintenance-icon {
            font-size: 80px;
            color: #667eea;
            margin-bottom: 30px;
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.1); }
            100% { transform: scale(1); }
        }
        
        h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 20px;
            font-weight: 300;
        }
        
        .subtitle {
            color: #7f8c8d;
            font-size: 1.2em;
            margin-bottom: 40px;
            line-height: 1.6;
        }
        
        .status-message {
            background: #f8f9fa;
            border-left: 4px solid #667eea;
            padding: 20px;
            margin: 30px 0;
            border-radius: 5px;
            text-align: left;
        }
        
        .status-message h3 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .status-message p {
            color: #5a6c7d;
            line-height: 1.6;
        }
        
        .contact-info {
            background: #e8f4fd;
            border-radius: 10px;
            padding: 25px;
            margin: 30px 0;
        }
        
        .contact-info h3 {
            color: #2980b9;
            margin-bottom: 15px;
        }
        
        .email-link {
            color: #3498db;
            text-decoration: none;
            font-weight: 600;
            font-size: 1.1em;
            padding: 10px 20px;
            background: white;
            border-radius: 25px;
            display: inline-block;
            margin-top: 10px;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(52, 152, 219, 0.2);
        }
        
        .email-link:hover {
            background: #3498db;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        }
        
        .footer {
            margin-top: 40px;
            color: #95a5a6;
            font-size: 0.9em;
        }
        
        .retry-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1em;
            cursor: pointer;
            margin: 20px 10px;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }
        
        .retry-button:hover {
            background: #5a67d8;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
        
        @media (max-width: 768px) {
            .maintenance-container {
                padding: 40px 20px;
            }
            
            h1 {
                font-size: 2em;
            }
            
            .maintenance-icon {
                font-size: 60px;
            }
        }
    </style>
</head>
<body>
    <div class="maintenance-container">
        <div class="maintenance-icon">🔧</div>
        
        <h1>Server Maintenance</h1>
        <p class="subtitle">The GRS Wall Designer application is currently unavailable. Our team is working to resolve this issue.</p>
        
        <div class="status-message">
            <h3>What happened?</h3>
            <p>The server encountered an issue and couldn't start properly. This could be due to:</p>
            <ul style="margin: 10px 0; padding-left: 20px; color: #5a6c7d;">
                <li>Database connection issues</li>
                <li>Server configuration problems</li>
                <li>Temporary system maintenance</li>
                <li>Resource limitations</li>
            </ul>
        </div>
        
        <div class="contact-info">
            <h3>Need Immediate Help?</h3>
            <p>If you need urgent assistance or want to report this issue, please contact our support team:</p>
            <a href="mailto:<EMAIL>" class="email-link">
                📧 <EMAIL>
            </a>
        </div>
        
        <button class="retry-button" onclick="window.location.reload()">
            🔄 Try Again
        </button>
        
        <button class="retry-button" onclick="window.history.back()" style="background: #95a5a6;">
            ← Go Back
        </button>
        
        <div class="footer">
            <p><strong>GRS Wall Designer</strong> - Geosynthetic Reinforced Soil Wall Design Tool</p>
            <p>If this problem persists, please include the current time and any error details in your email.</p>
        </div>
    </div>
    
    <script>
        // Auto-refresh every 2 minutes to check if server is back
        setTimeout(function() {
            window.location.reload();
        }, 120000);
        
        // Show current time for error reporting
        document.addEventListener('DOMContentLoaded', function() {
            const now = new Date();
            const timeString = now.toLocaleString();
            const footer = document.querySelector('.footer');
            footer.innerHTML += `<p style="margin-top: 15px; font-size: 0.8em; color: #bdc3c7;">Error occurred at: ${timeString}</p>`;
        });
    </script>
</body>
</html>
