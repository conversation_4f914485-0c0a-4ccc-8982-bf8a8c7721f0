"""
GRS Wall Designer - SINGLE CONFIGURATION FILE
All settings in one place. Optimized for 2-core, 1GB RAM system.
"""

import os
import logging
from datetime import timedelta

class DevelopmentConfig:
    """Development Mode Configuration"""

    # Flask Settings
    DEBUG = True
    ENV = 'development'
    SECRET_KEY = 'dev-secret-key-change-in-production'

    # Database Settings
    MYSQL_HOST = 'localhost'
    MYSQL_USER = 'root'
    MYSQL_PASSWORD = 'sriroot'
    MYSQL_DB = 'grs_software'
    MYSQL_POOL_SIZE = 5  # Lower for development
    MYSQL_POOL_TIMEOUT = 20
    MYSQL_POOL_RECYCLE = 3600
    MYSQL_AUTOCOMMIT = True

    # Server Settings
    HOST = '0.0.0.0'
    PORT = 5000

    # Logging Settings
    LOG_LEVEL = logging.INFO
    LOG_TO_CONSOLE = True
    LOG_FILE = 'logs/grs_app.log'

    # Session Settings
    SESSION_COOKIE_SECURE = False
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)

class ProductionConfig:
    """Production Mode - Optimized for 2-core, 1GB RAM system"""

    # Flask Settings
    DEBUG = False
    ENV = 'production'
    SECRET_KEY = 'GRS_Wall_Designer_Production_Key_2024_Secure_Random_String_12345'

    # Database Settings
    MYSQL_HOST = 'localhost'
    MYSQL_USER = 'root'
    MYSQL_PASSWORD = 'sriroot'
    MYSQL_DB = 'grs_software'
    MYSQL_POOL_SIZE = 8  # Optimized for 2-core system
    MYSQL_POOL_TIMEOUT = 30
    MYSQL_POOL_RECYCLE = 3600
    MYSQL_AUTOCOMMIT = True

    # Server Settings
    HOST = '0.0.0.0'
    PORT = 5000

    # Logging Settings
    LOG_LEVEL = logging.WARNING  # Reduced logging for performance
    LOG_TO_CONSOLE = False
    LOG_FILE = 'logs/grs_app.log'

    # Session Settings
    SESSION_COOKIE_SECURE = False  # Set to True when using HTTPS
    SESSION_COOKIE_HTTPONLY = True
    SESSION_COOKIE_SAMESITE = 'Lax'
    PERMANENT_SESSION_LIFETIME = timedelta(minutes=30)

def get_config():
    """Get configuration based on FLASK_ENV"""
    env = os.environ.get('FLASK_ENV', 'development').lower()

    if env == 'production':
        return ProductionConfig
    else:
        return DevelopmentConfig
